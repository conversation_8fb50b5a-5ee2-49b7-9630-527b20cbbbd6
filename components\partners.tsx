'use client';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination } from 'swiper/modules';

import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import Image from 'next/image';
import { useMediaQuery } from '@mantine/hooks';
import Link from 'next/link';
import { Loader2, MapPin, Target } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { Partner } from '@/types';
import { BUCKET_BASE_URL } from '@/config';
import API from '@/lib/axios';
export default function Partners() {
  const isMobile = useMediaQuery('(max-width: 1080px)');
  const { data, isLoading } = useQuery({
    staleTime: 60 * 30 * 1000,
    queryKey: ['partner'],
    queryFn: async () => {
      const { data } = await API.get<Partner[]>(`/api/ads/ads`);
      return data;
    },
  });

  return (
    <section className="bg-path prose mx-auto mt-10 flex h-fit w-full flex-col items-center justify-center space-y-6 lg:prose-xl lg:lg:max-w-screen-xl lg:px-5 lg:py-10">
      <h2>Nos partenaires</h2>
      <div className="max-w-full px-1 lg:container md:p-0 lg:max-w-none">
        {isLoading ? (
          <Loader2 className="mx-auto h-5 w-5 animate-spin" />
        ) : (
          <Swiper
            spaceBetween={50}
            navigation={!isMobile}
            pagination={isMobile}
            modules={[Navigation, Pagination]}
            centeredSlides
            centeredSlidesBounds
            slidesPerView={1}
            className="box-border h-fit !w-full lg:w-[80%] lg:!px-12"
          >
            {data?.map((partner, index) => (
              <SwiperSlide key={partner._id} className="box-border !h-fit">
                <Link
                  passHref
                  target="_blank"
                  href={partner.linkTarget}
                  className="group relative flex h-[350px] w-[100%] flex-col overflow-hidden rounded-lg border backdrop-blur-md transition-shadow duration-300 hover:shadow-md hover:shadow-slate-200 hover:drop-shadow-md lg:w-[1200px]"
                >
                  <div className="hiddden absolute rounded-full p-4 font-bold group-hover:opacity-50 md:block">
                    <span className="">Click Here</span>
                  </div>

                  <div className="relative flex h-full w-full items-center justify-center">
                    <Image
                      src={`${BUCKET_BASE_URL}/${partner.adsPicture}`}
                      width={1080}
                      height={720}
                      alt="img-maintenance"
                      className="h-full w-full rounded-sm object-contain"
                    />
                  </div>
                  <div className="absolute bottom-0 left-0 right-0 flex rounded-md border-0 border-t bg-slate-400 bg-opacity-50 px-4 py-2 transition-all duration-500 md:hidden md:group-hover:flex">
                    <div className="flex w-full flex-col gap-1 text-sm font-semibold">
                      <div className="flex items-center gap-1 text-white">
                        <Target className="h-5 w-5 text-blue-400" />
                        <span className="!m-0">2500 visites</span>
                      </div>
                      <div className="flex items-center gap-1 text-white">
                        <MapPin className="h-5 w-5 text-blue-400" />
                        <span className="!m-0">{partner.localisation}</span>
                      </div>
                    </div>
                    <div className="flex w-full items-center justify-end">
                      <span className="text-white underline underline-offset-2">
                        Contacter
                      </span>
                    </div>
                  </div>
                </Link>
              </SwiperSlide>
            ))}
          </Swiper>
        )}
      </div>
    </section>
  );
}
