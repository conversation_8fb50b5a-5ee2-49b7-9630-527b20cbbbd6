'use client';

import { signIn, useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from '@/components/ui/use-toast';
import logger from '@/lib/logger';

interface SignInData {
  email: string;
  password: string;
}

interface UseSignInReturn {
  signInUser: (data: SignInData, callbackUrl?: string) => Promise<void>;
  isLoading: boolean;
}

export function useSignIn(): UseSignInReturn {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const {data: session} = useSession();

  const signInUser = async (data: SignInData, callbackUrl?: string) => {
    setIsLoading(true);

    try {
      const result = await signIn('credentials', {
        email: data.email.trim(),
        password: data.password,
        redirect: false,
        callbackUrl: callbackUrl || '/',
      });

      if (result?.error) {
        toast({
          title: 'Erreur de connexion',
          description: result.error,
          variant: 'destructive',
        });
        return;
      }

      if (result?.ok) {
        toast({
          title: 'Connexion réussie',
          description: 'Redirection en cours...',
        });

        // Attendre un peu pour que la session soit mise à jour
        await new Promise((resolve) => setTimeout(resolve, 200));

         const redirectUrl = '/dashboard/superdealer-groups';
      
        try {
          router.push(redirectUrl);
          // Si ça ne marche pas après 1 seconde, forcer avec window.location
          setTimeout(() => {
            if (window.location.pathname === '/signin') {
              // console.log('Redirection forcée avec window.location');
              window.location.href = redirectUrl;
            }
          }, 1000);
        } catch (routerError) {
          window.location.href = redirectUrl;
        }
      }
    } catch (error) {
      logger.error('Erreur lors de la connexion:', error);
      toast({
        title: 'Erreur',
        description: "Une erreur inattendue s'est produite",
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return {
    signInUser,
    isLoading,
  };
}
